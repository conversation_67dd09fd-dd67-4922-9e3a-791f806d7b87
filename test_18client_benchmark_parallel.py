#!/usr/bin/env python3
"""
并行基准测试脚本：18客户端，10维，110次评估（50初始+60迭代），Non-IID分区，1轮
使用多进程加速计算
"""

import numpy as np
import pandas as pd
import time
import os
from pyiaf.parallel_iaf_fbo import ParallelIAF_FBO
from pyiaf.Tasks.benchmark import create_tasks_diff_func

def run_single_experiment(run_id, n_clients=18, dimension=10, n_initial=50, max_iterations=60):
    """运行单次实验"""

    total_evaluations = n_initial + max_iterations  # 110次评估

    print(f"开始实验 {run_id}/20:")
    print(f"- 客户端数量: {n_clients}")
    print(f"- 问题维度: {dimension}")
    print(f"- 初始采样: {n_initial}")
    print(f"- 最大迭代: {max_iterations}")
    print(f"- 总评估次数: {total_evaluations}")
    print(f"- 边界策略: 函数特定边界 (与MATLAB一致)")
    print("-" * 50)
    
    # 创建18个不同的任务函数
    tasks = create_tasks_diff_func(dim=dimension, normalized=False)
    if run_id == 1:  # 只在第一轮打印详细信息
        print(f"创建了 {len(tasks)} 个基准任务")

        # 使用函数特定的边界（与MATLAB版本一致）
        client_bounds = []
        for task in tasks:
            if hasattr(task, 'x_lb') and hasattr(task, 'x_ub'):
                lower = np.full(dimension, task.x_lb)
                upper = np.full(dimension, task.x_ub)
                client_bounds.append((lower, upper))
            else:
                # 默认边界
                lower = np.full(dimension, -5.0)
                upper = np.full(dimension, 5.0)
                client_bounds.append((lower, upper))

        print(f"函数特定边界设置完成")

        # 打印每个客户端的边界信息
        print("客户端边界信息:")
        for i, (task, bounds) in enumerate(zip(tasks, client_bounds)):
            task_name = type(task).__name__
            print(f"  客户端{i:2d}: {task_name:12s} 边界=[{bounds[0][0]:6.1f}, {bounds[1][0]:6.1f}]")
        print()
    else:
        # 其他轮次只设置边界，不打印详细信息
        client_bounds = []
        for task in tasks:
            if hasattr(task, 'x_lb') and hasattr(task, 'x_ub'):
                lower = np.full(dimension, task.x_lb)
                upper = np.full(dimension, task.x_ub)
                client_bounds.append((lower, upper))
            else:
                lower = np.full(dimension, -5.0)
                upper = np.full(dimension, 5.0)
                client_bounds.append((lower, upper))
    
    # 创建并行IAF-FBO优化器 (与MATLAB参数完全对齐)
    # 使用不同的随机种子确保每轮实验的独立性
    optimizer = ParallelIAF_FBO(
        n_clients=n_clients,
        bounds=client_bounds,  # 使用函数特定边界
        n_initial=n_initial,    # N = 50 (MATLAB)
        max_iterations=max_iterations,  # MAXFE = 60 (MATLAB)
        af_type='LCB',         # UCB_Flag = 2 (MATLAB)
        n_clusters=6,          # cl_num = 6 (MATLAB)
        pop_size=100,          # popsize = 100 (MATLAB)
        cso_iters=100,         # wmax = 100 (MATLAB)
        transfer_prob=0.5,     # rand < 0.5 (MATLAB)
        noise_prob=0.0,        # p = 0 (MATLAB)
        random_state=42 + run_id,  # 每轮使用不同的随机种子
        n_processes=None  # 使用所有可用CPU核心
    )

    if run_id == 1:
        print(f"并行IAF-FBO优化器创建完成")

    # 设置目标函数 (避免lambda函数的pickle问题)
    objective_functions = {}
    for client_id in range(n_clients):
        objective_functions[client_id] = tasks[client_id]

    if run_id == 1:
        print(f"目标函数设置完成")
    
    # 运行优化
    if run_id == 1:
        print("开始并行优化...")
    else:
        print(f"运行实验 {run_id}/20...")

    start_time = time.time()

    optimizer.setup_clients(objective_functions)
    results = optimizer.run_optimization()

    end_time = time.time()
    total_time = end_time - start_time

    print(f"实验 {run_id} 完成，耗时: {total_time:.2f}秒")
    if run_id == 1:
        print("-" * 50)
    
    # 输出结果
    best_values = []
    for client_id in range(n_clients):
        best_val = results['best_values'][client_id]
        best_values.append(best_val)
        if run_id == 1:  # 只在第一轮打印详细结果
            print(f"客户端 {client_id:2d}: 最优值 = {best_val:10.6f}")

    if run_id == 1:
        print("-" * 50)
        print(f"统计信息:")
        print(f"- 平均最优值: {np.mean(best_values):.6f}")
        print(f"- 最优值标准差: {np.std(best_values):.6f}")
        print(f"- 最好结果: {np.min(best_values):.6f}")
        print(f"- 最差结果: {np.max(best_values):.6f}")

    # 保存每个客户端的110次真实评估值（转置格式：行为评估轮次，列为客户端）
    client_results = []
    for client_id in range(n_clients):
        eval_history = optimizer.history['evaluation_history'][client_id]
        # 确保每个客户端有110次评估
        if len(eval_history) >= 110:
            client_110_values = eval_history[:110]
        else:
            # 如果不足110次，用最后的值填充
            client_110_values = eval_history + [eval_history[-1]] * (110 - len(eval_history))

        client_results.append(client_110_values)

    # 转换为DataFrame并转置：行为评估轮次，列为客户端
    client_df = pd.DataFrame(client_results).T  # 转置
    client_df.columns = [f'Client_{i}' for i in range(n_clients)]  # 设置列名为客户端
    client_df.index.name = 'Evaluation_Round'  # 设置行索引名为评估轮次

    # 保存客户端评估结果（每轮保存独立文件）
    client_filename = f"client_evaluations_18clients_10d_110evals_run{run_id:02d}.csv"
    client_df.to_csv(client_filename)
    if run_id == 1:
        print(f"客户端评估历史已保存到: {client_filename} (行：评估轮次，列：客户端)")

    return results, total_time, best_values

def run_20_experiments():
    """运行20轮完整实验"""

    print("="*60)
    print("开始20轮IAF-FBO基准测试实验")
    print("="*60)

    all_results = []
    all_best_values = []
    total_start_time = time.time()

    for run_id in range(1, 21):
        try:
            results, runtime, best_values = run_single_experiment(run_id)
            all_results.append(results)
            all_best_values.append(best_values)

            # 显示当前轮次的简要统计
            print(f"实验 {run_id:2d}: 平均={np.mean(best_values):8.3f}, "
                  f"最好={np.min(best_values):8.3f}, "
                  f"最差={np.max(best_values):8.3f}, "
                  f"耗时={runtime:6.1f}s")

        except Exception as e:
            print(f"实验 {run_id} 失败: {str(e)}")
            continue

    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time

    # 计算20轮实验的总体统计
    print("\n" + "="*60)
    print("20轮实验完成！总体统计结果:")
    print("="*60)

    if all_best_values:
        # 转换为numpy数组便于计算
        all_best_values = np.array(all_best_values)  # shape: (20, 18)

        # 每个客户端在20轮中的统计
        print("\n各客户端在20轮实验中的统计:")
        print("客户端  平均值      标准差      最好值      最差值")
        print("-" * 55)
        for client_id in range(18):
            client_values = all_best_values[:, client_id]
            mean_val = np.mean(client_values)
            std_val = np.std(client_values)
            min_val = np.min(client_values)
            max_val = np.max(client_values)
            print(f"客户端{client_id:2d}: {mean_val:10.3f} {std_val:10.3f} {min_val:10.3f} {max_val:10.3f}")

        # 每轮实验的总体统计
        print(f"\n每轮实验的总体统计:")
        print(f"- 总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.1f}分钟)")
        print(f"- 平均每轮时间: {total_runtime/len(all_best_values):.2f}秒")
        print(f"- 成功完成轮数: {len(all_best_values)}/20")

        # 保存20轮实验的汇总结果
        summary_df = pd.DataFrame(all_best_values,
                                columns=[f'Client_{i}' for i in range(18)])
        summary_df.index = [f'Run_{i+1:02d}' for i in range(len(all_best_values))]
        summary_df.index.name = 'Experiment_Run'

        summary_filename = "experiment_summary_20runs_18clients.csv"
        summary_df.to_csv(summary_filename)
        print(f"- 20轮实验汇总结果已保存到: {summary_filename}")

        return all_results, total_runtime
    else:
        print("没有成功完成的实验！")
        return [], total_runtime


if __name__ == "__main__":
    try:
        all_results, total_runtime = run_20_experiments()
        print(f"\n所有实验成功完成！总运行时间: {total_runtime:.2f}秒")
    except Exception as e:
        print(f"实验过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
