"""
并行版本的IAF-FBO：使用多进程加速计算
"""

import numpy as np
import pandas as pd
import time
import os
from multiprocessing import Pool, cpu_count
from functools import partial
from .iaf_fbo import IAF_FBO
from .gaussian_process import GaussianProcess
from .neural_classifier import NeuralClassifier, train_pairwise_classifier
from .competitive_swarm_optimizer import CompetitiveSwarmOptimizer
from .federated_framework import FederatedServer, FederatedClient
from .utils import lhs_classic, acquisition_function, create_pairwise_data
import warnings
warnings.filterwarnings('ignore')


def _train_client_model(args):
    """
    并行训练单个客户端的模型
    
    Args:
        args: (client_id, client_data, af_type, noise_prob, random_state)
    
    Returns:
        (client_id, trained_classifier)
    """
    client_id, X_data, y_data, bounds, af_type, noise_prob, random_state = args
    
    try:
        # 创建GP模型
        gp_model = GaussianProcess(random_state=random_state + client_id if random_state else None)
        
        # 标准化y数据
        y_norm = (y_data - np.min(y_data)) / (np.max(y_data) - np.min(y_data) + 1e-10)
        gp_model.fit(X_data, y_norm)
        
        # 生成样本用于分类器训练
        n_samples = 100
        X_sample_norm = np.random.uniform(0, 1, (n_samples, len(bounds[0])))
        X_sample = X_sample_norm * (bounds[1] - bounds[0]) + bounds[0]
        
        # GP预测
        mu_sample, sigma_sample = gp_model.predict(X_sample, return_std=True)
        
        # 计算采集函数值
        y_best = np.min(y_norm)
        af_values = acquisition_function(mu_sample, sigma_sample, y_best, af_type)
        
        # 合并数据
        X_combined = np.vstack([X_data, X_sample])
        
        # 计算现有数据的AF值
        mu_data, sigma_data = gp_model.predict(X_data, return_std=True)
        af_data = acquisition_function(mu_data, sigma_data, y_best, af_type)
        
        af_combined = np.concatenate([af_data, af_values])
        
        # 训练分类器
        classifier = train_pairwise_classifier(
            X_combined, af_combined, bounds, 
            noise_prob=noise_prob,
            random_state=random_state + client_id if random_state else None
        )
        
        return client_id, classifier, gp_model
        
    except Exception as e:
        print(f"客户端 {client_id} 模型训练失败: {e}")
        return client_id, None, None


def _optimize_client_acquisition(args):
    """
    并行优化单个客户端的采集函数
    
    Args:
        args: (client_id, classifier, bounds, pop_size, cso_iters, initial_pop, random_state)
    
    Returns:
        (client_id, best_point)
    """
    client_id, classifier, bounds, pop_size, cso_iters, initial_pop, random_state = args
    
    try:
        if classifier is None:
            # 如果分类器训练失败，随机采样
            dim = len(bounds[0])
            best_point = np.random.uniform(bounds[0], bounds[1], dim)
            return client_id, best_point
        
        # 使用CSO优化
        cso = CompetitiveSwarmOptimizer(
            pop_size=pop_size,
            max_iters=cso_iters,
            random_state=random_state + client_id if random_state else None
        )
        
        best_point, _ = cso.optimize(classifier, bounds, initial_pop)
        return client_id, best_point
        
    except Exception as e:
        print(f"客户端 {client_id} 采集函数优化失败: {e}")
        # 回退到随机采样
        dim = len(bounds[0])
        best_point = np.random.uniform(bounds[0], bounds[1], dim)
        return client_id, best_point


def _evaluate_client_objective(args):
    """
    并行评估单个客户端的目标函数
    
    Args:
        args: (client_id, new_point, objective_function)
    
    Returns:
        (client_id, new_point, new_value, eval_time)
    """
    client_id, new_point, objective_function = args
    
    try:
        eval_start_time = time.time()
        new_value = objective_function(new_point)
        eval_time = time.time() - eval_start_time
        
        return client_id, new_point, new_value, eval_time
        
    except Exception as e:
        print(f"客户端 {client_id} 目标函数评估失败: {e}")
        return client_id, new_point, float('inf'), 0.0


class ParallelIAF_FBO(IAF_FBO):
    """
    并行版本的IAF-FBO优化器
    """
    
    def __init__(self, n_clients, bounds, n_initial=20, max_iterations=50, 
                 af_type='LCB', n_clusters=3, pop_size=100, cso_iters=100,
                 transfer_prob=0.5, noise_prob=0.0, random_state=None, 
                 n_processes=None):
        """
        初始化并行IAF-FBO优化器
        
        Args:
            n_processes: 并行进程数，None表示使用所有CPU核心
            其他参数与IAF_FBO相同
        """
        super().__init__(n_clients, bounds, n_initial, max_iterations, 
                        af_type, n_clusters, pop_size, cso_iters,
                        transfer_prob, noise_prob, random_state)
        
        # 设置并行进程数
        if n_processes is None:
            self.n_processes = min(cpu_count(), n_clients)
        else:
            self.n_processes = min(n_processes, n_clients)
        
        print(f"使用 {self.n_processes} 个进程进行并行计算")
    
    def _train_local_models(self):
        """
        并行训练本地模型
        """
        # 准备参数
        args_list = []
        for client_id, client in self.clients.items():
            X_data = client.X_data
            y_data = client.y_data
            bounds = client.bounds
            
            args_list.append((
                client_id, X_data, y_data, bounds, 
                self.af_type, self.noise_prob, self.random_state
            ))
        
        # 并行训练
        with Pool(processes=self.n_processes) as pool:
            results = pool.map(_train_client_model, args_list)
        
        # 更新客户端模型
        for client_id, classifier, gp_model in results:
            if classifier is not None and gp_model is not None:
                self.clients[client_id].classifier = classifier
                self.clients[client_id].gp_model = gp_model
    
    def _optimize_acquisition_functions(self):
        """
        并行优化采集函数
        """
        new_points = {}
        
        # 准备参数
        args_list = []
        for client_id, client in self.clients.items():
            # 选择分类器
            group_id = self.server.get_client_group(client_id)
            if (np.random.random() < self.transfer_prob and
                group_id is not None and
                group_id in self.server.global_classifiers):
                classifier = self.server.global_classifiers[group_id]
            else:
                classifier = client.classifier
            
            # 准备初始种群
            initial_pop = None
            if len(client.new_X) > 0:
                initial_pop = np.array(client.new_X[-min(10, len(client.new_X)):])
                if len(initial_pop.shape) == 3:
                    initial_pop = initial_pop.reshape(-1, initial_pop.shape[-1])
            
            args_list.append((
                client_id, classifier, client.bounds, 
                self.pop_size, self.cso_iters, initial_pop, self.random_state
            ))
        
        # 并行优化
        with Pool(processes=self.n_processes) as pool:
            results = pool.map(_optimize_client_acquisition, args_list)
        
        # 收集结果
        for client_id, best_point in results:
            new_points[client_id] = best_point
        
        return new_points
    
    def _evaluate_and_update(self, new_points):
        """
        并行评估和更新
        """
        # 准备参数
        args_list = []
        for client_id, new_point in new_points.items():
            if client_id in self.objective_functions:
                args_list.append((
                    client_id, new_point, self.objective_functions[client_id]
                ))
        
        # 并行评估
        with Pool(processes=self.n_processes) as pool:
            results = pool.map(_evaluate_client_objective, args_list)
        
        # 更新客户端数据
        for client_id, new_point, new_value, eval_time in results:
            if new_value != float('inf'):
                # 更新客户端数据
                self.clients[client_id].update_data(new_point, new_value)
                
                # 记录评估历史
                self.history['evaluation_history'][client_id].append(new_value)
                self.history['evaluation_times'][client_id].append(eval_time)
