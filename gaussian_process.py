"""
Gaussian Process implementation for IAF-FBO algorithm
"""

import numpy as np
from scipy.optimize import minimize
from scipy.linalg import cholesky, solve_triangular, LinAlgError
from .utils import rbf_kernel
import warnings
warnings.filterwarnings('ignore')


class GaussianProcess:
    """
    Gaussian Process regression model
    """
    
    def __init__(self, kernel='rbf', theta_bounds=(1e-5, 100.0), nugget=1e-10, 
                 normalize_y=True, random_state=None):
        """
        Initialize Gaussian Process
        
        Args:
            kernel: Kernel type ('rbf')
            theta_bounds: Bounds for hyperparameter optimization
            nugget: Regularization parameter
            normalize_y: Whether to normalize target values
            random_state: Random seed
        """
        self.kernel = kernel
        self.theta_bounds = theta_bounds
        self.nugget = nugget
        self.normalize_y = normalize_y
        self.random_state = random_state
        
        # Model parameters
        self.X_train = None
        self.y_train = None
        self.theta = None
        self.y_mean = None
        self.y_std = None
        self.K_inv = None
        self.alpha = None
        
    def fit(self, X, y, theta_init=None):
        """
        Fit the Gaussian Process model
        
        Args:
            X: Training inputs of shape (n_samples, n_features)
            y: Training targets of shape (n_samples,)
            theta_init: Initial hyperparameters
        """
        X = np.asarray(X)
        y = np.asarray(y).ravel()
        
        if X.ndim != 2:
            raise ValueError("X must be 2D array")
        
        self.X_train = X.copy()
        self.y_train = y.copy()
        
        # Normalize targets if requested
        if self.normalize_y:
            self.y_mean = np.mean(y)
            self.y_std = np.std(y)
            if self.y_std == 0:
                self.y_std = 1.0
            y_norm = (y - self.y_mean) / self.y_std
        else:
            self.y_mean = 0.0
            self.y_std = 1.0
            y_norm = y
        
        # Initialize hyperparameters
        if theta_init is None:
            if self.theta is None:
                self.theta = 5.0 * np.ones(X.shape[1])
            # else keep previous theta as initialization
        else:
            self.theta = np.asarray(theta_init)
        
        # Optimize hyperparameters
        self._optimize_hyperparameters(X, y_norm)
        
        # Compute kernel matrix and its inverse
        K = self._compute_kernel_matrix(X, X, self.theta)
        
        try:
            # Use Cholesky decomposition for numerical stability
            L = cholesky(K, lower=True)
            self.alpha = solve_triangular(L, y_norm, lower=True)
            self.alpha = solve_triangular(L.T, self.alpha, lower=False)
            self.K_inv = solve_triangular(L.T, 
                                        solve_triangular(L, np.eye(len(K)), lower=True), 
                                        lower=False)
        except LinAlgError:
            # Fallback to regular inversion with increased nugget
            K_reg = K + (self.nugget * 1000) * np.eye(len(K))
            self.K_inv = np.linalg.inv(K_reg)
            self.alpha = self.K_inv @ y_norm
    
    def predict(self, X, return_std=True):
        """
        Make predictions with the Gaussian Process
        
        Args:
            X: Test inputs of shape (n_samples, n_features)
            return_std: Whether to return prediction uncertainty
        
        Returns:
            y_mean: Predicted means
            y_std: Predicted standard deviations (if return_std=True)
        """
        if self.X_train is None:
            raise ValueError("Model must be fitted before making predictions")
        
        X = np.asarray(X)
        if X.ndim == 1:
            X = X.reshape(1, -1)
        
        # Compute kernel between test and training points
        K_star = self._compute_kernel_matrix(X, self.X_train, self.theta)
        
        # Predict mean
        y_mean = K_star @ self.alpha
        
        # Denormalize predictions
        y_mean = y_mean * self.y_std + self.y_mean
        
        if return_std:
            # Compute prediction variance
            K_star_star = self._compute_kernel_matrix(X, X, self.theta)
            v = solve_triangular(cholesky(self._compute_kernel_matrix(self.X_train, self.X_train, self.theta), lower=True), 
                               K_star.T, lower=True)
            y_var = np.diag(K_star_star) - np.sum(v**2, axis=0)
            y_var = np.maximum(y_var, 0)  # Ensure non-negative variance
            
            # Denormalize variance
            y_std = np.sqrt(y_var) * self.y_std
            
            return y_mean, y_std
        else:
            return y_mean
    
    def _compute_kernel_matrix(self, X1, X2, theta):
        """
        Compute kernel matrix between two sets of points
        """
        if self.kernel == 'rbf':
            return rbf_kernel(X1, X2, theta, self.nugget)
        else:
            raise ValueError(f"Unknown kernel: {self.kernel}")
    
    def _optimize_hyperparameters(self, X, y):
        """
        Optimize hyperparameters by maximizing log marginal likelihood
        """
        def objective(theta):
            return -self._log_marginal_likelihood(X, y, theta)
        
        # Set bounds for optimization
        bounds = [self.theta_bounds] * X.shape[1]
        
        # Try multiple random initializations
        best_theta = self.theta.copy()
        best_obj = objective(best_theta)
        
        if self.random_state is not None:
            np.random.seed(self.random_state)
        
        for _ in range(3):  # Try 3 random initializations
            theta_init = np.random.uniform(self.theta_bounds[0], self.theta_bounds[1], X.shape[1])
            
            try:
                result = minimize(objective, theta_init, bounds=bounds, method='L-BFGS-B')
                if result.success and result.fun < best_obj:
                    best_theta = result.x
                    best_obj = result.fun
            except:
                continue
        
        self.theta = best_theta
    
    def _log_marginal_likelihood(self, X, y, theta):
        """
        Compute log marginal likelihood
        """
        try:
            K = self._compute_kernel_matrix(X, X, theta)
            L = cholesky(K, lower=True)
            
            alpha = solve_triangular(L, y, lower=True)
            alpha = solve_triangular(L.T, alpha, lower=False)
            
            # Log marginal likelihood
            log_likelihood = -0.5 * y.T @ alpha - np.sum(np.log(np.diag(L))) - 0.5 * len(y) * np.log(2 * np.pi)
            
            return log_likelihood
        except (LinAlgError, np.linalg.LinAlgError):
            return -np.inf
    
    def get_params(self):
        """
        Get model parameters
        """
        return {
            'theta': self.theta.copy() if self.theta is not None else None,
            'y_mean': self.y_mean,
            'y_std': self.y_std
        }
    
    def set_params(self, params):
        """
        Set model parameters
        """
        if 'theta' in params and params['theta'] is not None:
            self.theta = params['theta'].copy()
        if 'y_mean' in params:
            self.y_mean = params['y_mean']
        if 'y_std' in params:
            self.y_std = params['y_std']
